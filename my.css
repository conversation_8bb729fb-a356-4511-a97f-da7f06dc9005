body { 
    font-family: sans-serif;
    height: 100vh;
    background: radial-gradient( ellipse at bottom, #1b2735,#090a0f right, );
    overflow: hidden;
}
h1 {
    font-weight: normal;
    color: white;
    text-align: center;
    width: 100vw;
    font-size: 50px;
    position: absolute;
    top: 50px;
    font-family: Alfa slab one, cursive;
}
#button-container {
    position:  relative;
    top: 40vh;
    left: 50%;
    transform: translate(-50%,);
    width: fit-content;
    height: fit-content;
    
}
button {
    background-color:rgb(22, 22, 22) ;
    border: 1px solid white;
    padding: 10px 15px;
    font-size: 25px;
    margin-left: 145px;
    border-radius: 1rem;
    box-shadow: 0 0 10px 0 rgba(197, 197, 197,);
    color: white;
    animation: exppanding-animation 4s infinite;

    
}
 @keyframes expanding-animation {
    0%, 100% {
        box-shadow: 0px 0px 5px 2px rgba(197, 197, 197,);
         
    }
    50% { 
        box-shadow: 0px 0px 10px 5px rgba(197, 197, 197,);
         
    
 }
 100% {
    box-shadow: 0px 0px 5px 2px rgba(197, 197, 197,);
}
}
#button-container:hover #menu{
    opacity: 1;
     transform: translateY(0px);
    
}
#button-container:hover #main-button {
    background-color:  white;
    color: rgb(22, 22, 22);
     
}
#menu {
    position: absolute;
    transition: 500ms;
    transform: translateY(-50px);
     margin-top: 20px;
     color: white;
   padding: 5px;
border-radius: 1rem;
border: 1px solid white;

box-shadow: 0 0 10px 0 rgba(197, 197, 197,);
    background-color: rgb(22, 22, 22);
    opacity: 0;
}

#main-button {
    margin: 20px;

}
.btn{
transition: 500ms;
}
#btn1:hover{
    background-size: cover;
    scale: 10;
background-position: center;
background-repeat: no-repeat;
background-image: url("https://wallpaperaccess.com/full/19602.jpg");
content: none;
}
#button2:hover{
    background-size: cover;
    scale: 10;
background-position: center;
background-repeat: no-repeat;
background-image: url("https://wallpaperaccess.com/full/527447.jpg");
content: none;
}

#button3:hover{
    background-size: cover;
    scale: 10;
background-position: center;
background-repeat: no-repeat;
background-image: url("https://wallpaperaccess.com/full/361282.jpg");
content: none;
}

#earth{
    z-index: -1;
    position: absolute;
    top: 50%;
    left: 50%;
     
    width: 200%;
    height: 100%;
    color: white;
    box-shadow: 0 40 20px 0 rgba(60, 105, 138,0.85);
    background-image: radial-gradient( ellipse at bottom, rgb( 60, 105 ,138,0.85 ) 0%);
    white 100% ;
    
}
border-radius: 80%;
 left:50%;
 transform : translateX( -50%);
 
color: white;
bottom:-70%;
 



